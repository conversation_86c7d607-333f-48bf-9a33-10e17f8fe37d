# AI Text Detector System - User Manual

## Table of Contents
1. [System Overview](#system-overview)
2. [System Requirements](#system-requirements)
3. [Installation Guide](#installation-guide)
4. [Getting Started](#getting-started)
5. [User Interface Guide](#user-interface-guide)
6. [Features and Functionality](#features-and-functionality)
7. [File Upload and Analysis](#file-upload-and-analysis)
8. [Understanding Results](#understanding-results)
9. [Account Management](#account-management)
10. [Troubleshooting](#troubleshooting)
11. [Technical Specifications](#technical-specifications)
12. [API Documentation](#api-documentation)

---

## System Overview

The AI Text Detector System is a comprehensive web-based application designed to analyze and classify text content as either AI-generated, human-written, or mixed content. The system utilizes advanced machine learning algorithms powered by OpenAI's GPT-4 models to provide accurate detection with detailed confidence scores and reasoning.

### Key Features
- **Advanced AI Detection**: State-of-the-art algorithms using OpenAI GPT-4o models
- **Multi-Format Support**: Analyze text directly or upload TXT, DOCX, PDF files
- **Real-time Processing**: Instant analysis with intelligent caching
- **Detailed Analytics**: Comprehensive breakdowns with confidence scores and sub-metrics
- **User Authentication**: Secure login system with password recovery
- **Analysis History**: Complete tracking with searchable records
- **Modern UI**: Responsive design with dark/light mode support
- **API Integration**: RESTful API for programmatic access

---

## System Requirements

### Server Requirements
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **PHP**: Version 7.4 or higher (8.0+ recommended)
- **Database**: MySQL 5.7+ or MariaDB 10.3+
- **Python**: Version 3.8+ (for AI analysis engine)
- **Storage**: Minimum 1GB free space
- **Memory**: 512MB RAM minimum (2GB recommended)

### Client Requirements
- **Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **JavaScript**: Enabled
- **Internet Connection**: Required for AI analysis

### Dependencies
- **PHP Extensions**: mysqli, json, session, fileinfo
- **Composer Packages**: phpoffice/phpword, smalot/pdfparser
- **Python Packages**: openai, requests, logging

---

## Installation Guide

### Step 1: Environment Setup
1. Install XAMPP or similar web server stack
2. Ensure PHP 7.4+ and MySQL are running
3. Install Python 3.8+ and pip
4. Install Composer (PHP dependency manager)

### Step 2: Database Setup
1. Create a new MySQL database named `ai_text_detector_db`
2. Import the database schema:
   ```sql
   mysql -u root -p ai_text_detector_db < database/schema.sql
   ```
3. Update database credentials in `db.php`

### Step 3: Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Python dependencies
pip install openai requests python-dotenv
```

### Step 4: Configuration
1. Set up OpenAI API key in environment variables:
   ```bash
   export OPENAI_API_KEY="your-api-key-here"
   ```
2. Configure file permissions for uploads and logs directories
3. Test the installation using `test_system.php`

---

## Getting Started

### First Time Setup
1. Navigate to your installation URL (e.g., `http://localhost/my_freakin_thesis/`)
2. Create a new account using the registration form
3. Verify your email address (if email verification is enabled)
4. Log in with your credentials

### Quick Start Guide
1. **Login**: Access the system through `index.php` or `login.html`
2. **Navigate**: Use the main service page to start analyzing text
3. **Analyze**: Enter text or upload a file for analysis
4. **Review**: Examine the detailed results and confidence scores
5. **History**: View your previous analyses in the history section

---

## User Interface Guide

### Login Interface
The system provides two login interfaces:
- **Classic Interface** (`index.php`): Traditional PHP-based login
- **Modern Interface** (`login.html`): Enhanced UI with improved UX

#### Login Features:
- Username or email authentication
- Password visibility toggle
- "Remember me" functionality
- Password recovery option
- New user registration

### Main Service Interface
The primary analysis interface (`service.php`) includes:
- **Text Input Area**: Large textarea for content analysis
- **Word Counter**: Real-time word count (max 2000 words)
- **Analysis Button**: Triggers the AI detection process
- **Results Panel**: Displays classification and confidence scores
- **History Section**: Shows previous analyses

### Navigation Menu
- **Home**: Dashboard with system overview
- **Service**: Main analysis interface
- **About Us**: Information about the system and developers
- **Profile**: User account management
- **Logout**: Secure session termination

---

## Features and Functionality

### Text Analysis
1. **Direct Text Input**:
   - Paste or type text directly into the analysis area
   - Maximum limit: 2000 words
   - Real-time word counting
   - Instant analysis upon submission

2. **Classification Types**:
   - **AI-Generated**: Content likely created by AI
   - **Human-Written**: Content likely written by humans
   - **Mixed**: Content with both AI and human elements
   - **Abstract**: Academic abstracts or summaries

3. **Confidence Scoring**:
   - Percentage-based confidence (0-100%)
   - Color-coded indicators (red, yellow, green)
   - Detailed reasoning for classification

### Advanced Analytics
- **Sub-scores**: Creativity, authenticity, linguistic complexity
- **Processing Time**: Analysis duration tracking
- **Model Information**: AI model used for analysis
- **Session Tracking**: Unique session identifiers

---

## File Upload and Analysis

### Supported File Formats
- **TXT**: Plain text files
- **DOCX**: Microsoft Word documents
- **PDF**: Portable Document Format files

### Upload Process
1. Click the "Upload File" button
2. Select a supported file format
3. Wait for file processing and text extraction
4. Review extracted text before analysis
5. Proceed with AI analysis

### File Size Limits
- Maximum file size: 10MB
- Text extraction preserves formatting
- Large files may take longer to process

---

## Understanding Results

### Classification Indicators
- **🤖 AI-Generated**: Red indicator, high AI probability
- **👤 Human-Written**: Green indicator, high human probability
- **🔄 Mixed**: Yellow indicator, combination of both
- **📄 Abstract**: Blue indicator, academic content

### Confidence Levels
- **High Confidence** (80-100%): Very reliable classification
- **Medium Confidence** (60-79%): Moderately reliable
- **Low Confidence** (0-59%): Less certain, manual review recommended

### Detailed Reasoning
Each analysis includes:
- Explanation of classification decision
- Key indicators that influenced the result
- Linguistic patterns identified
- Recommendations for content verification

---

## Account Management

### Profile Settings
- Update personal information
- Change password
- Email preferences
- Analysis history preferences

### Security Features
- Password strength requirements
- Login attempt monitoring
- Session timeout protection
- Secure password reset

### Usage Statistics
- Total analyses performed
- Average confidence scores
- Most recent activity
- Monthly usage trends

---

## Troubleshooting

### Common Issues

#### Login Problems
- **Issue**: Cannot log in with correct credentials
- **Solution**: Clear browser cache, check caps lock, verify account status

#### Analysis Errors
- **Issue**: "Analysis failed" error message
- **Solution**: Check internet connection, verify text length, try again

#### File Upload Issues
- **Issue**: File upload fails or times out
- **Solution**: Check file size (max 10MB), verify file format, ensure stable connection

#### Performance Issues
- **Issue**: Slow analysis or page loading
- **Solution**: Check server resources, clear browser cache, optimize database

### Error Codes
- **E001**: Database connection failed
- **E002**: Invalid authentication credentials
- **E003**: File upload error
- **E004**: AI analysis service unavailable
- **E005**: Text length exceeds maximum limit

### Getting Help
- Check system logs in `/logs/` directory
- Review error messages for specific guidance
- Contact system administrator for technical issues
- Consult documentation for feature questions

---

## Technical Specifications

### Architecture Overview
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: PHP 7.4+, MySQL 5.7+
- **AI Engine**: Python 3.8+, OpenAI GPT-4
- **Security**: Session-based authentication, SQL injection protection

### Database Schema
The system uses a normalized database structure with the following main tables:
- `users`: User account information
- `analysis_sessions`: Analysis results and metadata
- `feedback`: User feedback and ratings
- `login_attempts`: Security monitoring
- `file_uploads`: File processing tracking

### Performance Metrics
- **Average Analysis Time**: 2-5 seconds
- **Concurrent Users**: Up to 100 simultaneous users
- **Daily Analysis Limit**: 100 per user
- **Uptime Target**: 99.5%

---

## API Documentation

### Authentication Endpoints
```
POST /api/auth/login
POST /api/auth/register
POST /api/auth/logout
POST /api/auth/forgot-password
```

### Analysis Endpoints
```
POST /api/analysis/text
POST /api/analysis/file
GET /api/analysis/history
GET /api/analysis/details
```

### User Management
```
GET /api/user/profile
PUT /api/user/profile
GET /api/user/stats
```

### Example API Usage
```javascript
// Analyze text via API
const response = await fetch('/api/analysis/text', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
        text: "Your text to analyze here"
    })
});

const result = await response.json();
console.log(result.classification, result.confidence);
```

---

## Appendix Information

This user manual serves as a comprehensive guide for the AI Text Detector System and can be referenced as Appendix A in academic documentation. The system represents a complete implementation of modern web technologies combined with advanced AI capabilities for text analysis and classification.

**Document Version**: 1.0  
**Last Updated**: July 2025  
**System Version**: 2.0.0  
**Compatibility**: All modern web browsers and mobile devices
